-- CreateEnum
CREATE TYPE "public"."WageInterval" AS ENUM ('HOURLY', 'MONTHLY', 'YEARLY');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "public"."LocationRequirement" AS ENUM ('REMOTE', 'ON_SITE', 'HYBRID');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "public"."ExperienceLevel" AS ENUM ('JUNIOR', 'MID', 'SENIOR');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "public"."JobListingStatus" AS ENUM ('DRAFT', 'PUBLISHED', 'DELISTED');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "public"."JobType" AS ENUM ('FULL_TIME', 'PART_TIME', 'CONTRACT', 'INTERNSHIP');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "public"."ApplicationStatus" AS ENUM ('APPLIED', 'SHORTLISTED', 'REJECTED', 'ACCEPTED');

-- CreateTable
CREATE TABLE "public"."User" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "imageUrl" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."UserNotificationSettings" (
    "userId" TEXT NOT NULL,
    "newJobEmailNotifications" BOOLEAN NOT NULL DEFAULT false,
    "aiPrompt" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserNotificationSettings_pkey" PRIMARY KEY ("userId")
);

-- CreateTable
CREATE TABLE "public"."UserResume" (
    "userId" TEXT NOT NULL,
    "resumeFileUrl" TEXT NOT NULL,
    "resumeFileKey" TEXT NOT NULL,
    "aiSummary" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserResume_pkey" PRIMARY KEY ("userId")
);

-- CreateTable
CREATE TABLE "public"."Organization" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "imageUrl" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Organization_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."OrganizationUserSettings" (
    "userId" TEXT NOT NULL,
    "organizationId" TEXT NOT NULL,
    "newApplicationEmailNotifications" BOOLEAN NOT NULL DEFAULT false,
    "minimumRating" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "OrganizationUserSettings_pkey" PRIMARY KEY ("userId","organizationId")
);

-- CreateTable
CREATE TABLE "public"."JobListing" (
    "id" UUID NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "wage" INTEGER,
    "wageInterval" "public"."WageInterval",
    "location" TEXT,
    "isFeatured" BOOLEAN NOT NULL DEFAULT false,
    "locationRequirement" "public"."LocationRequirement",
    "experienceLevel" "public"."ExperienceLevel",
    "jobListingStatus" "public"."JobListingStatus" NOT NULL DEFAULT 'DRAFT',
    "jobType" "public"."JobType",
    "postedAt" TIMESTAMP(3),
    "timeZone" TEXT NOT NULL DEFAULT 'UTC',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "organizationId" TEXT NOT NULL,

    CONSTRAINT "JobListing_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."JobApplication" (
    "id" UUID NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "rating" INTEGER,
    "coverLetter" TEXT,
    "applicationStatus" "public"."ApplicationStatus" NOT NULL DEFAULT 'APPLIED',
    "userId" TEXT NOT NULL,
    "jobListingId" UUID NOT NULL,
    "organizationId" TEXT NOT NULL,

    CONSTRAINT "JobApplication_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "public"."User"("email");

-- CreateIndex
CREATE INDEX "User_email_idx" ON "public"."User"("email");

-- CreateIndex
CREATE INDEX "OrganizationUserSettings_organizationId_idx" ON "public"."OrganizationUserSettings"("organizationId");

-- CreateIndex
CREATE INDEX "JobListing_organizationId_idx" ON "public"."JobListing"("organizationId");

-- CreateIndex
CREATE INDEX "JobApplication_userId_idx" ON "public"."JobApplication"("userId");

-- CreateIndex
CREATE INDEX "JobApplication_jobListingId_idx" ON "public"."JobApplication"("jobListingId");

-- CreateIndex
CREATE INDEX "JobApplication_organizationId_idx" ON "public"."JobApplication"("organizationId");

-- AddForeignKey
ALTER TABLE "public"."UserNotificationSettings" ADD CONSTRAINT "UserNotificationSettings_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."UserResume" ADD CONSTRAINT "UserResume_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."OrganizationUserSettings" ADD CONSTRAINT "OrganizationUserSettings_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."OrganizationUserSettings" ADD CONSTRAINT "OrganizationUserSettings_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "public"."Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."JobListing" ADD CONSTRAINT "JobListing_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "public"."Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."JobApplication" ADD CONSTRAINT "JobApplication_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."JobApplication" ADD CONSTRAINT "JobApplication_jobListingId_fkey" FOREIGN KEY ("jobListingId") REFERENCES "public"."JobListing"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."JobApplication" ADD CONSTRAINT "JobApplication_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "public"."Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

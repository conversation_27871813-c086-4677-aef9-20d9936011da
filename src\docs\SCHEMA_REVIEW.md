# Prisma Schema Review

This document explains the **Prisma schema** used in this project.  
It is meant to help contributors understand how models, relations, and enums are structured.

---

## 📌 Overview

- **Database**: PostgreSQL  
- **ORM**: Prisma Client (generated in `../generated/prisma`)  
- **ID Strategy**:
  - Users & Organizations use **Clerk IDs** (string-based, e.g. `user_123`).
  - Most other entities use **UUIDs** (`@default(uuid()) @db.Uuid`).

---

## 👤 User Model

```prisma
model User {
  id        String   @id
  name      String
  imageUrl  String
  email     String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  notificationSettings     UserNotificationSettings?
  resume                   UserResume?
  organizationUserSettings OrganizationUserSettings[]
  jobApplications          JobApplication[]

  @@index([email])
}
```

- **Primary key**: `id` (Clerk ID)  
- **Unique constraint**: `email`  
- **Relations**:
  - One-to-one with `UserNotificationSettings`
  - One-to-one with `UserResume`
  - Many-to-many with `Organization` (through `OrganizationUserSettings`)
  - One-to-many with `JobApplication`

---

## 🔔 UserNotificationSettings

```prisma
model UserNotificationSettings {
  userId                   String   @id
  newJobEmailNotifications Boolean  @default(false)
  aiPrompt                 String?
  createdAt                DateTime @default(now())
  updatedAt                DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}
```

- **One-to-one** relation with `User` (cascades on delete).  
- Stores **notification preferences** & optional AI prompt text.

---

## 📄 UserResume

```prisma
model UserResume {
  userId        String   @id
  resumeFileUrl String
  resumeFileKey String
  aiSummary     String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}
```

- Each user can upload **one resume**.  
- Stored with a **file URL + storage key**.  
- AI-generated **summary** field for quick insights.  

---

## 🏢 Organization

```prisma
model Organization {
  id        String   @id
  name      String
  imageUrl  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  jobListings              JobListing[]
  organizationUserSettings OrganizationUserSettings[]
  jobApplications          JobApplication[]
}
```

- Uses **Clerk/external IDs**.  
- Can have many **job listings**, **users** (via settings), and **applications**.

---

## ⚙️ OrganizationUserSettings

```prisma
model OrganizationUserSettings {
  userId         String
  organizationId String
  newApplicationEmailNotifications Boolean @default(false)
  minimumRating  Int?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@id([userId, organizationId])
}
```

- **Composite key** = `(userId, organizationId)`  
- Defines **per-organization settings** for a user.  
- Supports **minimum rating filter** for candidate management.  

---

## 📋 JobListing

```prisma
model JobListing {
  id                  String   @id @default(uuid()) @db.Uuid
  title               String
  description         String?
  wage                Int?
  wageInterval        WageInterval?
  location            String?
  isFeatured          Boolean  @default(false)
  locationRequirement LocationRequirement?
  experienceLevel     ExperienceLevel?
  jobListingStatus    JobListingStatus @default(DRAFT)
  jobType             JobType?
  postedAt            DateTime?
  timeZone            String @default("UTC")
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  jobApplications JobApplication[]
}
```

- **UUID IDs** for job listings.  
- Rich metadata:
  - Wage + interval
  - Location requirement (remote/on-site/hybrid)
  - Experience level
  - Status (draft/published/delisted)
- **Relations**:
  - Belongs to an organization  
  - Can have multiple job applications  

---

## 📝 JobApplication

```prisma
model JobApplication {
  id                String   @id @default(uuid()) @db.Uuid
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  rating            Int?
  coverLetter       String?
  applicationStatus ApplicationStatus @default(APPLIED)

  userId          String
  user            User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  jobListingId    String
  jobListing      JobListing   @relation(fields: [jobListingId], references: [id], onDelete: Cascade)

  organizationId  String
  organization    Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
}
```

- Tracks **applications per job listing**.  
- Supports:
  - **Ratings** (for screening)
  - **Cover letter**
  - **Application status** (applied/shortlisted/rejected/accepted)  

---

## 🧩 Enums

```prisma
enum WageInterval { HOURLY MONTHLY YEARLY }
enum LocationRequirement { REMOTE ON_SITE HYBRID }
enum ExperienceLevel { JUNIOR MID SENIOR }
enum JobListingStatus { DRAFT PUBLISHED DELISTED }
enum JobType { FULL_TIME PART_TIME CONTRACT INTERNSHIP }
enum ApplicationStatus { APPLIED SHORTLISTED REJECTED ACCEPTED }
```

These enums provide consistency for job listings & applications.

---

## 🔑 Design Notes

- **Cascade deletes**:  
  Removing a user/organization cascades to related records (`settings`, `resume`, `applications`).
- **Indexes**:  
  Added on commonly queried fields (`email`, `organizationId`, `userId`, `jobListingId`).
- **Normalization**:  
  Split into small, well-defined models for flexibility (e.g., separate `UserResume`, `UserNotificationSettings`).

---

✅ With this schema, contributors can confidently extend models (e.g., add interview stages, AI scoring, or analytics) without breaking existing relations.

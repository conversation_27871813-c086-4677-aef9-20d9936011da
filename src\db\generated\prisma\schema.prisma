// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id // Clerk provides IDs (like "user_123"), so no @default needed
  name      String
  imageUrl  String
  email     String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  notificationSettings     UserNotificationSettings?
  resume                   UserResume?
  organizationUserSettings OrganizationUserSettings[]
  jobApplications          JobApplication[]

  @@index([email])
}

model UserNotificationSettings {
  userId                   String   @id
  newJobEmailNotifications Boolean  @default(false)
  aiPrompt                 String?
  createdAt                DateTime @default(now())
  updatedAt                DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model UserResume {
  userId        String   @id
  resumeFileUrl String
  resumeFileKey String
  aiSummary     String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Organization {
  id        String   @id // From Clerk or external system
  name      String
  imageUrl  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  jobListings              JobListing[]
  organizationUserSettings OrganizationUserSettings[]
  jobApplications          JobApplication[]
}

model OrganizationUserSettings {
  userId                           String
  organizationId                   String
  newApplicationEmailNotifications Boolean  @default(false)
  minimumRating                    Int?
  createdAt                        DateTime @default(now())
  updatedAt                        DateTime @updatedAt

  // Relations
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@id([userId, organizationId])
  @@index([organizationId])
}

model JobListing {
  id                  String               @id @default(uuid()) @db.Uuid
  title               String
  description         String?
  wage                Int?
  wageInterval        WageInterval?
  location            String?
  isFeatured          Boolean              @default(false)
  locationRequirement LocationRequirement?
  experienceLevel     ExperienceLevel?
  jobListingStatus    JobListingStatus     @default(DRAFT)
  jobType             JobType?
  postedAt            DateTime?
  timeZone            String               @default("UTC")
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt

  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  jobApplications JobApplication[]

  @@index([organizationId])
}

model JobApplication {
  id                String            @id @default(uuid()) @db.Uuid
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  rating            Int?
  coverLetter       String?
  applicationStatus ApplicationStatus @default(APPLIED)

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  jobListingId String     @db.Uuid
  jobListing   JobListing @relation(fields: [jobListingId], references: [id], onDelete: Cascade)

  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([jobListingId])
  @@index([organizationId])
}

// ==================== ENUMS ====================

enum WageInterval {
  HOURLY
  MONTHLY
  YEARLY
}

enum LocationRequirement {
  REMOTE
  ON_SITE
  HYBRID
}

enum ExperienceLevel {
  JUNIOR
  MID
  SENIOR
}

enum JobListingStatus {
  DRAFT
  PUBLISHED
  DELISTED
}

enum JobType {
  FULL_TIME
  PART_TIME
  CONTRACT
  INTERNSHIP
}

enum ApplicationStatus {
  APPLIED
  SHORTLISTED
  REJECTED
  ACCEPTED
}

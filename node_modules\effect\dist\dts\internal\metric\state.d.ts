import type * as MetricState from "../../MetricState.js";
/**
 * @since 2.0.0
 * @category refinements
 */
export declare const isFrequencyState: (u: unknown) => u is MetricState.MetricState.Frequency;
/**
 * @since 2.0.0
 * @category refinements
 */
export declare const isGaugeState: (u: unknown) => u is MetricState.MetricState.Gauge<number | bigint>;
/**
 * @since 2.0.0
 * @category refinements
 */
export declare const isHistogramState: (u: unknown) => u is MetricState.MetricState.Histogram;
/**
 * @since 2.0.0
 * @category refinements
 */
export declare const isSummaryState: (u: unknown) => u is MetricState.MetricState.Summary;
//# sourceMappingURL=state.d.ts.map

/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
  PrismaClientRustPanicError,
  PrismaClientInitializationError,
  PrismaClientValidationError,
  getPrismaClient,
  sqltag,
  empty,
  join,
  raw,
  skip,
  Decimal,
  Debug,
  objectEnumValues,
  makeStrictEnum,
  Extensions,
  warnOnce,
  defineDmmfProperty,
  Public,
  getRuntime,
  createParam,
} = require('./runtime/wasm-engine-edge.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.16.2
 * Query Engine version: 1c57fdcd7e44b29b9313256c76699e91c3ac3c43
 */
Prisma.prismaVersion = {
  client: "6.16.2",
  engine: "1c57fdcd7e44b29b9313256c76699e91c3ac3c43"
}

Prisma.PrismaClientKnownRequestError = PrismaClientKnownRequestError;
Prisma.PrismaClientUnknownRequestError = PrismaClientUnknownRequestError
Prisma.PrismaClientRustPanicError = PrismaClientRustPanicError
Prisma.PrismaClientInitializationError = PrismaClientInitializationError
Prisma.PrismaClientValidationError = PrismaClientValidationError
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = sqltag
Prisma.empty = empty
Prisma.join = join
Prisma.raw = raw
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = Extensions.getExtensionContext
Prisma.defineExtension = Extensions.defineExtension

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}





/**
 * Enums
 */
exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  imageUrl: 'imageUrl',
  email: 'email',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserNotificationSettingsScalarFieldEnum = {
  userId: 'userId',
  newJobEmailNotifications: 'newJobEmailNotifications',
  aiPrompt: 'aiPrompt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserResumeScalarFieldEnum = {
  userId: 'userId',
  resumeFileUrl: 'resumeFileUrl',
  resumeFileKey: 'resumeFileKey',
  aiSummary: 'aiSummary',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrganizationScalarFieldEnum = {
  id: 'id',
  name: 'name',
  imageUrl: 'imageUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrganizationUserSettingsScalarFieldEnum = {
  userId: 'userId',
  organizationId: 'organizationId',
  newApplicationEmailNotifications: 'newApplicationEmailNotifications',
  minimumRating: 'minimumRating',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.JobListingScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  wage: 'wage',
  wageInterval: 'wageInterval',
  location: 'location',
  isFeatured: 'isFeatured',
  locationRequirement: 'locationRequirement',
  experienceLevel: 'experienceLevel',
  jobListingStatus: 'jobListingStatus',
  jobType: 'jobType',
  postedAt: 'postedAt',
  timeZone: 'timeZone',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  organizationId: 'organizationId'
};

exports.Prisma.JobApplicationScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  rating: 'rating',
  coverLetter: 'coverLetter',
  applicationStatus: 'applicationStatus',
  userId: 'userId',
  jobListingId: 'jobListingId',
  organizationId: 'organizationId'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.WageInterval = exports.$Enums.WageInterval = {
  HOURLY: 'HOURLY',
  MONTHLY: 'MONTHLY',
  YEARLY: 'YEARLY'
};

exports.LocationRequirement = exports.$Enums.LocationRequirement = {
  REMOTE: 'REMOTE',
  ON_SITE: 'ON_SITE',
  HYBRID: 'HYBRID'
};

exports.ExperienceLevel = exports.$Enums.ExperienceLevel = {
  JUNIOR: 'JUNIOR',
  MID: 'MID',
  SENIOR: 'SENIOR'
};

exports.JobListingStatus = exports.$Enums.JobListingStatus = {
  DRAFT: 'DRAFT',
  PUBLISHED: 'PUBLISHED',
  DELISTED: 'DELISTED'
};

exports.JobType = exports.$Enums.JobType = {
  FULL_TIME: 'FULL_TIME',
  PART_TIME: 'PART_TIME',
  CONTRACT: 'CONTRACT',
  INTERNSHIP: 'INTERNSHIP'
};

exports.ApplicationStatus = exports.$Enums.ApplicationStatus = {
  APPLIED: 'APPLIED',
  SHORTLISTED: 'SHORTLISTED',
  REJECTED: 'REJECTED',
  ACCEPTED: 'ACCEPTED'
};

exports.Prisma.ModelName = {
  User: 'User',
  UserNotificationSettings: 'UserNotificationSettings',
  UserResume: 'UserResume',
  Organization: 'Organization',
  OrganizationUserSettings: 'OrganizationUserSettings',
  JobListing: 'JobListing',
  JobApplication: 'JobApplication'
};
/**
 * Create the Client
 */
const config = {
  "generator": {
    "name": "client",
    "provider": {
      "fromEnvVar": null,
      "value": "prisma-client-js"
    },
    "output": {
      "value": "E:\\job-ai\\src\\db\\generated\\prisma",
      "fromEnvVar": null
    },
    "config": {
      "engineType": "library"
    },
    "binaryTargets": [
      {
        "fromEnvVar": null,
        "value": "windows",
        "native": true
      }
    ],
    "previewFeatures": [],
    "sourceFilePath": "E:\\job-ai\\src\\db\\prisma\\schema.prisma",
    "isCustomOutput": true
  },
  "relativeEnvPaths": {
    "rootEnvPath": null,
    "schemaEnvPath": "../../../../.env"
  },
  "relativePath": "../../prisma",
  "clientVersion": "6.16.2",
  "engineVersion": "1c57fdcd7e44b29b9313256c76699e91c3ac3c43",
  "datasourceNames": [
    "db"
  ],
  "activeProvider": "postgresql",
  "inlineDatasources": {
    "db": {
      "url": {
        "fromEnvVar": "DATABASE_URL",
        "value": null
      }
    }
  },
  "inlineSchema": "// This is your Prisma schema file,\n// learn more about it in the docs: https://pris.ly/d/prisma-schema\n\n// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?\n// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init\n\ngenerator client {\n  provider = \"prisma-client-js\"\n  output   = \"../generated/prisma\"\n}\n\ndatasource db {\n  provider = \"postgresql\"\n  url      = env(\"DATABASE_URL\")\n}\n\nmodel User {\n  id        String   @id // Clerk provides IDs (like \"user_123\"), so no @default needed\n  name      String\n  imageUrl  String\n  email     String   @unique\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  // Relations\n  notificationSettings     UserNotificationSettings?\n  resume                   UserResume?\n  organizationUserSettings OrganizationUserSettings[]\n  jobApplications          JobApplication[]\n\n  @@index([email])\n}\n\nmodel UserNotificationSettings {\n  userId                   String   @id\n  newJobEmailNotifications Boolean  @default(false)\n  aiPrompt                 String?\n  createdAt                DateTime @default(now())\n  updatedAt                DateTime @updatedAt\n\n  // Relations\n  user User @relation(fields: [userId], references: [id], onDelete: Cascade)\n}\n\nmodel UserResume {\n  userId        String   @id\n  resumeFileUrl String\n  resumeFileKey String\n  aiSummary     String?\n  createdAt     DateTime @default(now())\n  updatedAt     DateTime @updatedAt\n\n  // Relations\n  user User @relation(fields: [userId], references: [id], onDelete: Cascade)\n}\n\nmodel Organization {\n  id        String   @id // From Clerk or external system\n  name      String\n  imageUrl  String?\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  // Relations\n  jobListings              JobListing[]\n  organizationUserSettings OrganizationUserSettings[]\n  jobApplications          JobApplication[]\n}\n\nmodel OrganizationUserSettings {\n  userId                           String\n  organizationId                   String\n  newApplicationEmailNotifications Boolean  @default(false)\n  minimumRating                    Int?\n  createdAt                        DateTime @default(now())\n  updatedAt                        DateTime @updatedAt\n\n  // Relations\n  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)\n  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)\n\n  @@id([userId, organizationId])\n  @@index([organizationId])\n}\n\nmodel JobListing {\n  id                  String               @id @default(uuid()) @db.Uuid\n  title               String\n  description         String?\n  wage                Int?\n  wageInterval        WageInterval?\n  location            String?\n  isFeatured          Boolean              @default(false)\n  locationRequirement LocationRequirement?\n  experienceLevel     ExperienceLevel?\n  jobListingStatus    JobListingStatus     @default(DRAFT)\n  jobType             JobType?\n  postedAt            DateTime?\n  timeZone            String               @default(\"UTC\")\n  createdAt           DateTime             @default(now())\n  updatedAt           DateTime             @updatedAt\n\n  organizationId String\n  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)\n\n  jobApplications JobApplication[]\n\n  @@index([organizationId])\n}\n\nmodel JobApplication {\n  id                String            @id @default(uuid()) @db.Uuid\n  createdAt         DateTime          @default(now())\n  updatedAt         DateTime          @updatedAt\n  rating            Int?\n  coverLetter       String?\n  applicationStatus ApplicationStatus @default(APPLIED)\n\n  userId String\n  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  jobListingId String     @db.Uuid\n  jobListing   JobListing @relation(fields: [jobListingId], references: [id], onDelete: Cascade)\n\n  organizationId String\n  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)\n\n  @@index([userId])\n  @@index([jobListingId])\n  @@index([organizationId])\n}\n\n// ==================== ENUMS ====================\n\nenum WageInterval {\n  HOURLY\n  MONTHLY\n  YEARLY\n}\n\nenum LocationRequirement {\n  REMOTE\n  ON_SITE\n  HYBRID\n}\n\nenum ExperienceLevel {\n  JUNIOR\n  MID\n  SENIOR\n}\n\nenum JobListingStatus {\n  DRAFT\n  PUBLISHED\n  DELISTED\n}\n\nenum JobType {\n  FULL_TIME\n  PART_TIME\n  CONTRACT\n  INTERNSHIP\n}\n\nenum ApplicationStatus {\n  APPLIED\n  SHORTLISTED\n  REJECTED\n  ACCEPTED\n}\n",
  "inlineSchemaHash": "fd17f597c525273d91026590b5efdca64fb1b74a6f7f9e725ae08609a8919a68",
  "copyEngine": true
}
config.dirname = '/'

config.runtimeDataModel = JSON.parse("{\"models\":{\"User\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"imageUrl\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"email\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"notificationSettings\",\"kind\":\"object\",\"type\":\"UserNotificationSettings\",\"relationName\":\"UserToUserNotificationSettings\"},{\"name\":\"resume\",\"kind\":\"object\",\"type\":\"UserResume\",\"relationName\":\"UserToUserResume\"},{\"name\":\"organizationUserSettings\",\"kind\":\"object\",\"type\":\"OrganizationUserSettings\",\"relationName\":\"OrganizationUserSettingsToUser\"},{\"name\":\"jobApplications\",\"kind\":\"object\",\"type\":\"JobApplication\",\"relationName\":\"JobApplicationToUser\"}],\"dbName\":null},\"UserNotificationSettings\":{\"fields\":[{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"newJobEmailNotifications\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"aiPrompt\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"UserToUserNotificationSettings\"}],\"dbName\":null},\"UserResume\":{\"fields\":[{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"resumeFileUrl\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"resumeFileKey\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"aiSummary\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"UserToUserResume\"}],\"dbName\":null},\"Organization\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"imageUrl\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"jobListings\",\"kind\":\"object\",\"type\":\"JobListing\",\"relationName\":\"JobListingToOrganization\"},{\"name\":\"organizationUserSettings\",\"kind\":\"object\",\"type\":\"OrganizationUserSettings\",\"relationName\":\"OrganizationToOrganizationUserSettings\"},{\"name\":\"jobApplications\",\"kind\":\"object\",\"type\":\"JobApplication\",\"relationName\":\"JobApplicationToOrganization\"}],\"dbName\":null},\"OrganizationUserSettings\":{\"fields\":[{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"organizationId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"newApplicationEmailNotifications\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"minimumRating\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"OrganizationUserSettingsToUser\"},{\"name\":\"organization\",\"kind\":\"object\",\"type\":\"Organization\",\"relationName\":\"OrganizationToOrganizationUserSettings\"}],\"dbName\":null},\"JobListing\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"title\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"wage\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"wageInterval\",\"kind\":\"enum\",\"type\":\"WageInterval\"},{\"name\":\"location\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"isFeatured\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"locationRequirement\",\"kind\":\"enum\",\"type\":\"LocationRequirement\"},{\"name\":\"experienceLevel\",\"kind\":\"enum\",\"type\":\"ExperienceLevel\"},{\"name\":\"jobListingStatus\",\"kind\":\"enum\",\"type\":\"JobListingStatus\"},{\"name\":\"jobType\",\"kind\":\"enum\",\"type\":\"JobType\"},{\"name\":\"postedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"timeZone\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"organizationId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"organization\",\"kind\":\"object\",\"type\":\"Organization\",\"relationName\":\"JobListingToOrganization\"},{\"name\":\"jobApplications\",\"kind\":\"object\",\"type\":\"JobApplication\",\"relationName\":\"JobApplicationToJobListing\"}],\"dbName\":null},\"JobApplication\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"rating\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"coverLetter\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"applicationStatus\",\"kind\":\"enum\",\"type\":\"ApplicationStatus\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"JobApplicationToUser\"},{\"name\":\"jobListingId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"jobListing\",\"kind\":\"object\",\"type\":\"JobListing\",\"relationName\":\"JobApplicationToJobListing\"},{\"name\":\"organizationId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"organization\",\"kind\":\"object\",\"type\":\"Organization\",\"relationName\":\"JobApplicationToOrganization\"}],\"dbName\":null}},\"enums\":{},\"types\":{}}")
defineDmmfProperty(exports.Prisma, config.runtimeDataModel)
config.engineWasm = {
  getRuntime: async () => require('./query_engine_bg.js'),
  getQueryEngineWasmModule: async () => {
    const loader = (await import('#wasm-engine-loader')).default
    const engine = (await loader).default
    return engine
  }
}
config.compilerWasm = undefined

config.injectableEdgeEnv = () => ({
  parsed: {
    DATABASE_URL: typeof globalThis !== 'undefined' && globalThis['DATABASE_URL'] || typeof process !== 'undefined' && process.env && process.env.DATABASE_URL || undefined
  }
})

if (typeof globalThis !== 'undefined' && globalThis['DEBUG'] || typeof process !== 'undefined' && process.env && process.env.DEBUG || undefined) {
  Debug.enable(typeof globalThis !== 'undefined' && globalThis['DEBUG'] || typeof process !== 'undefined' && process.env && process.env.DEBUG || undefined)
}

const PrismaClient = getPrismaClient(config)
exports.PrismaClient = PrismaClient
Object.assign(exports, Prisma)


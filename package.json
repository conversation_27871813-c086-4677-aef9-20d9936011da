{"name": "job-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint", "db:generate": "prisma generate --schema=src/prisma/schema.prisma", "db:migrate": "prisma migrate dev --schema=src/prisma/schema.prisma --skip-generate", "db:studio": "prisma studio"}, "dependencies": {"@prisma/client": "^6.16.2", "next": "15.6.0-canary.8", "react": "19.1.0", "react-dom": "19.1.0", "zod": "^4.1.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.6.0-canary.8", "prisma": "^6.16.2", "tailwindcss": "^4", "typescript": "^5"}}